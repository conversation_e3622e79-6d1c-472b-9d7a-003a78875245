<!doctype html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">

    <!-- Primary Meta Tags -->
    <title>Free UX Masterclass - Learn AI-Powered Design Tools | Design Pirate</title>
    <meta name="title" content="Free UX Masterclass - Learn AI-Powered Design Tools | Design Pirate">
    <meta name="description" content="Join our FREE 2-hour live UX masterclass and master cutting-edge AI design tools. Learn from industry experts, get hands-on experience, and transform your design career. Limited seats available!">
    <meta name="keywords" content="UX design, UI design, AI design tools, free masterclass, design course, user experience, user interface, design training, online course, design pirate">
    <meta name="author" content="Design Pirate">
    <meta name="robots" content="index, follow">
    <meta name="language" content="English">
    <meta name="revisit-after" content="7 days">

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://design-pirates.web.app">
    <meta property="og:title" content="Free UX Masterclass - Learn AI-Powered Design Tools | Design Pirate">
    <meta property="og:description" content="Join our FREE 2-hour live UX masterclass and master cutting-edge AI design tools. Learn from industry experts, get hands-on experience, and transform your design career. Limited seats available!">
    <meta property="og:image" content="https://design-pirates.web.app/assets/ux-masterclass-og-image.jpg">
    <meta property="og:image:width" content="1200">
    <meta property="og:image:height" content="630">
    <meta property="og:site_name" content="Design Pirate">
    <meta property="og:locale" content="en_US">

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="https://design-pirates.web.app">
    <meta property="twitter:title" content="Free UX Masterclass - Learn AI-Powered Design Tools | Design Pirate">
    <meta property="twitter:description" content="Join our FREE 2-hour live UX masterclass and master cutting-edge AI design tools. Learn from industry experts, get hands-on experience, and transform your design career. Limited seats available!">
    <meta property="twitter:image" content="https://design-pirates.web.app/assets/ux-masterclass-twitter-image.jpg">
    <meta property="twitter:creator" content="@designpirate">
    <meta property="twitter:site" content="@designpirate">

    <!-- Canonical URL -->
    <link rel="canonical" href="https://design-pirates.web.app">

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png">
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png">
    <link rel="manifest" href="/site.webmanifest">

    <!-- Preconnect for performance -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link rel="preconnect" href="https://cdn.jsdelivr.net">
    <link rel="preconnect" href="https://cdnjs.cloudflare.com">

    <!-- CSS -->
    <link href="https://fonts.googleapis.com/css2?family=DM+Sans:ital,opsz,wght@0,9..40,100..1000;1,9..40,100..1000&display=swap" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.1/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-4bw+/aepP/YC94hEpVNVgiZdgIC5+VKNBQNGCHeKRQN+PtmoHDEXuppvnDJzQIu9" crossorigin="anonymous">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css" integrity="sha512-Evv84Mr4kqVGRNSgIGL/F/aIDqQb7xQ2vcrdIwxfjThSH8CSR7PBEakCr51Ck+w+/U6swU2Im1vVX0SVk9ABhg==" crossorigin="anonymous" referrerpolicy="no-referrer" />
    <!-- Swiper CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.css" />
    <link rel="stylesheet" href="styles/style.css">

    <!-- Firebase Configuration -->
    <script type="module">
        // Import Firebase modules
        import { initializeApp } from "https://www.gstatic.com/firebasejs/11.10.0/firebase-app.js";
        import { getAnalytics } from "https://www.gstatic.com/firebasejs/11.10.0/firebase-analytics.js";
        import { getFirestore, collection, addDoc, serverTimestamp } from "https://www.gstatic.com/firebasejs/11.10.0/firebase-firestore.js";

        // Your web app's Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyCFNNZcRf6o_xsdzT0r26x4sufaC6MwEJc",
            authDomain: "design-pirates.firebaseapp.com",
            projectId: "design-pirates",
            storageBucket: "design-pirates.firebasestorage.app",
            messagingSenderId: "838643384812",
            appId: "1:838643384812:web:d41fa034268b2a455000c9",
            measurementId: "G-ESFEF2FQX8"
        };

        // Initialize Firebase
        const app = initializeApp(firebaseConfig);
        const analytics = getAnalytics(app);
        const db = getFirestore(app);

        // Make Firebase available globally
        window.firebaseApp = app;
        window.firebaseAnalytics = analytics;
        window.firebaseDb = db;
        window.firebaseCollection = collection;
        window.firebaseAddDoc = addDoc;
        window.firebaseServerTimestamp = serverTimestamp;

        // Track page view
        console.log("Firebase initialized successfully");

        // Custom event tracking for masterclass registration
        window.trackMasterclassEvent = function(eventName, eventData = {}) {
            if (analytics) {
                import("https://www.gstatic.com/firebasejs/11.10.0/firebase-analytics.js")
                    .then(({ logEvent }) => {
                        logEvent(analytics, eventName, {
                            page_title: 'UX Masterclass Landing Page',
                            page_location: window.location.href,
                            ...eventData
                        });
                    });
            }
        };

        // Submit registration to Firebase
        window.submitRegistration = async function(registrationData) {
            try {
                console.log('Submitting registration data:', registrationData);

                // Add to Firestore registrations collection
                const docRef = await addDoc(collection(db, "registrations"), {
                    ...registrationData,
                    createdAt: serverTimestamp(),
                    updatedAt: serverTimestamp(),
                    status: 'registered',
                    source: 'landing_page'
                });

                // Track analytics event
                window.trackMasterclassEvent('ux_masterclass_registration', {
                    registration_id: docRef.id,
                    user_email: registrationData.email,
                    selected_date: registrationData.selectedDate,
                    selected_time: registrationData.selectedTime
                });

                console.log("Registration submitted successfully with ID: ", docRef.id);
                return { success: true, id: docRef.id };
            } catch (error) {
                console.error("Error submitting registration: ", error);
                return { success: false, error: error.message };
            }
        };
    </script>

    <!-- Structured Data - Course Schema -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "Course",
      "name": "UX Masterclass - AI-Powered Design Tools",
      "description": "A comprehensive 2-hour live masterclass covering AI-powered UX design tools and techniques. Learn from industry experts and get hands-on experience with cutting-edge design tools.",
      "provider": {
        "@type": "Organization",
        "name": "Design Pirate",
        "url": "https://design-pirates.web.app"
      },
      "courseMode": "online",
      "educationalLevel": "Beginner to Intermediate",
      "teaches": [
        "UX Design Fundamentals",
        "AI Design Tools",
        "User Interface Design",
        "Design Thinking",
        "Prototyping",
        "User Research"
      ],
      "duration": "PT2H",
      "inLanguage": "en",
      "isAccessibleForFree": true,
      "offers": {
        "@type": "Offer",
        "price": "0",
        "priceCurrency": "USD",
        "availability": "https://schema.org/InStock",
        "url": "https://design-pirates.web.app"
      },
      "hasCourseInstance": {
        "@type": "CourseInstance",
        "courseMode": "online",
        "instructor": {
          "@type": "Person",
          "name": "Design Pirate Instructor"
        }
      }
    }
    </script>

    <!-- Structured Data - Organization Schema -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "Organization",
      "name": "Design Pirate",
      "url": "https://design-pirates.web.app",
      "logo": "https://design-pirates.web.app/asserts/logo.svg",
      "description": "Design Pirate offers innovative design education and training programs, specializing in UX/UI design and AI-powered design tools.",
      "foundingDate": "2023",
      "contactPoint": {
        "@type": "ContactPoint",
        "contactType": "customer service",
        "email": "<EMAIL>"
      },
      "sameAs": [
        "https://facebook.com/designpirate",
        "https://linkedin.com/company/designpirate",
        "https://github.com/designpirate"
      ]
    }
    </script>

    <!-- Structured Data - Breadcrumb Schema -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "BreadcrumbList",
      "itemListElement": [
        {
          "@type": "ListItem",
          "position": 1,
          "name": "Home",
          "item": "https://design-pirates.web.app"
        },
        {
          "@type": "ListItem",
          "position": 2,
          "name": "UX Masterclass",
          "item": "https://design-pirates.web.app"
        }
      ]
    }
    </script>

    <!-- Structured Data - FAQ Schema -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "FAQPage",
      "mainEntity": [
        {
          "@type": "Question",
          "name": "Why is it free?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "We believe in making quality design education accessible to everyone. This free masterclass is our way of giving back to the design community and helping aspiring designers get started with AI-powered tools."
          }
        },
        {
          "@type": "Question",
          "name": "Do I need to install any tools?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "No installation required! All the AI design tools we'll be using are web-based. You just need a computer with internet access and a modern web browser."
          }
        },
        {
          "@type": "Question",
          "name": "Is this beginner-friendly?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Absolutely! This masterclass is designed for complete beginners. No prior design experience or coding knowledge is required. We'll guide you through everything step by step."
          }
        }
      ]
    }
    </script>
</head>

<body>
    <main>
    <div class="container-fluid intro p-0">

        <!-- Hero Section -->
        <header id="home" class="hero-section text-center">
            <div class="container">

                <nav class="navbar">
                    <div class="navbar-brand">
                        <a href="#" class="brand-text">
                            <span class="brand-design">Design</span>
                            <span class="brand-pirate">Pirate</span>
                        </a>
                    </div>

                    <!-- Desktop Navigation -->
                    <div class="navbar-container">
                        <ul class="nav nav-pills">
                            <li class="nav-item">
                                <a class="nav-link" href="#home">Home</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="#learn">Learn</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="#tools">Tools</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="#perks">Perks</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="#about">About</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="#faq">FAQ</a>
                            </li>
                        </ul>
                    </div>

                    <!-- Mobile Hamburger Menu -->
                    <div class="hamburger-menu" id="hamburgerMenu">
                        <span class="hamburger-line"></span>
                        <span class="hamburger-line"></span>
                        <span class="hamburger-line"></span>
                    </div>

                    <!-- Mobile Navigation Dropdown -->
                    <div class="mobile-nav-dropdown" id="mobileNavDropdown">
                        <ul class="mobile-nav-list">
                            <li class="mobile-nav-item">
                                <a class="mobile-nav-link" href="#home">Home</a>
                            </li>
                            <li class="mobile-nav-item">
                                <a class="mobile-nav-link" href="#learn">Learn</a>
                            </li>
                            <li class="mobile-nav-item">
                                <a class="mobile-nav-link" href="#tools">Tools</a>
                            </li>
                            <li class="mobile-nav-item">
                                <a class="mobile-nav-link" href="#perks">Perks</a>
                            </li>
                            <li class="mobile-nav-item">
                                <a class="mobile-nav-link" href="#about">About</a>
                            </li>
                            <li class="mobile-nav-item">
                                <a class="mobile-nav-link" href="#faq">FAQ</a>
                            </li>
                        </ul>
                    </div>
                </nav>
                <!-- Free Masterclass Badge -->
                <div class="masterclass-badge-new mb-4">
                    <span class="rocket-icon">🚀</span>
                    <span>FREE MASTERCLASS - LIMITED SEATS</span>
                </div>

                <h1 class="display-4 fw-bold">Design Real <span class="highlight">UI/UX</span> Projects</h1>
                <h1 class="display-4 fw-bold mb-4">in 2 Hours</h1>
                <h2 class="subtitle mb-4">Without Coding or Experience</h2>
                <p class="description mb-3">Join our Free Live UX Masterclass and learn how to build beautiful websites & apps using 7+ powerful AI design tools.</p>

                <div class="event-details d-flex justify-content-center gap-3 mb-5">
                    <div class="event-item">
                        June 21, 2025
                    </div>
                    <div class="event-item">
                        11 AM IST
                    </div>
                    <div class="event-item">
                        Live in Teams
                    </div>
                </div>

                <div class="d-flex justify-content-center gap-3 mb-4">
                    <a href="#" class="btn btn-register-primary" onclick="openModal()">
                        Register Now  <span class="strikethrough"> 1499</span>  - Free
                    </a>
                    <!-- <a href="#" class="btn btn-roadmap" onclick="openRoadmapModal()">
                        Download Road Map
                    </a> -->
                </div>

                <!-- Limited Seats Warning -->
                <div class="seats-warning-new">
                    <span class="warning-icon">👉</span>
                    <span>Seats Are Limited – Reserve Yours Now</span>
                </div>
            </div>
        </div>
    </header>

    <!-- What You'll Learn Section -->
    <section id="learn" class="learn-section" aria-labelledby="learn-heading">
        <div class="container">
            <div class="learn-header text-center mb-5">
                <h2 id="learn-heading" class="learn-title">What You'll Learn in<br>This 2-Hour Live Class</h2>
                <p class="learn-subtitle">Everything you need to start your UX journey<br>with AI-powered tools</p>
            </div>

            <div class="learn-content-wrapper">
                <div class="learn-star">
                    <img src="./asserts/Star 1.svg" alt="Star" class="star-decoration">
                </div>

                <div class="learn-content-box">
                    <ul class="learn-list">
                        <li>The difference between UI & UX — explained simply</li>
                        <li>How AI is transforming the design world</li>
                        <li>How to turn text into real UI screens in minutes</li>
                        <li>Hands-on with 7+ AI-powered design tools (Lovable, Figma AI, Visily & more)</li>
                        <li>Build your first live prototype — guided by our expert</li>
                        <li>Get access to free tools, templates, and a UX beginner roadmap</li>
                    </ul>
                </div>
            </div>
        </div>
    </section>

    <!-- AI Design Tools Section -->
    <section id="tools" class="ai-tools-section" aria-labelledby="tools-heading">
        <div class="container pb-4">
            <!-- Decorative Stars -->
            <div class="star-decoration star-top-left">
                <img src="./asserts/star-icon.svg" alt="Star" class="star-icon-svg">
            </div>
            <div class="star-decoration star-bottom-right">
                <img src="./asserts/star-icon.svg" alt="Star" class="star-icon-svg">
            </div>

            <div class="tools-header">
                <h2 id="tools-heading">AI Design Tools You'll<br>Use LIVE</h2>
                <p>Master these cutting-edge tools in our hands-on session</p>
            </div>

            <div class="tools-grid">
                 <div class="tool-card">
                    <img src="./asserts/whimsical.svg" alt="Whimsical" class="tool-icon">
                    <div class="tool-info">
                        <h3>Whimsical</h3>
                        <p>Wireframes & user flows from a prompt.</p>
                    </div>
                </div>
                 <div class="tool-card">
                    <img src="./asserts/miro.svg" alt="Miro" class="tool-icon">
                    <div class="tool-info">
                        <h3>Miro</h3>
                        <p>Visual brainstorming from a single idea.</p>
                    </div>
                </div>
                  <div class="tool-card">
                    <img src="./asserts/relume.svg" alt="Relume" class="tool-icon">
                    <div class="tool-info">
                        <h3>Relume</h3>
                        <p>AI Website Builder from Wireframe to UI.</p>
                    </div>
                </div>
                <div class="tool-card">
                    <img src="./asserts/lovable AI.svg" alt="Lovable AI - AI-powered app screen generator" class="tool-icon">
                    <div class="tool-info">
                        <h3>Lovable AI</h3>
                        <p>Turn your ideas into app screens in seconds.</p>
                    </div>
                </div>

                <div class="tool-card">
                    <img src="./asserts/bolt.svg" alt="Bolt - AI full-stack app builder" class="tool-icon">
                    <div class="tool-info">
                        <h3>Bolt</h3>
                        <p>Build full-stack apps by chatting with AI.</p>
                    </div>
                </div>

                <div class="tool-card">
                    <img src="./asserts/canva.svg" alt="Canva - UI mockup and design tool" class="tool-icon">
                    <div class="tool-info">
                        <h3>Canva</h3>
                        <p>Create UI mockups & social screens instantly.</p>
                    </div>
                </div>

                <div class="tool-card">
                    <img src="./asserts/framer.svg" alt="Framer - AI website builder from prompt to live site" class="tool-icon">
                    <div class="tool-info">
                        <h3>Framer</h3>
                        <p>From prompt to live website in minutes.</p>
                    </div>
                </div>

              

                <div class="tool-card">
                    <img src="./asserts/figma.svg" alt="Figma" class="tool-icon">
                    <div class="tool-info">
                        <h3>Figma</h3>
                        <p>Prompt-based UI component creation.</p>
                    </div>
                </div>

               

               
            </div>

            <!-- All tools message -->
            <div class="tools-message">
                <div class="tools-message-content">
                    <span class="check-icon"><i class="fa-solid fa-check"></i></span>
                    All tools have Free plans. No software installation needed.
                </div>
            </div>
        </div>
    </div>

    <!-- Everything You'll Receive Section -->
    <div id="perks" class="receive-section">
        <div class="container">
            <h2 class="receive-title">Everything You'll<br>Receive <span class="price-highlight"><s>9,999</s></span>
                (Free)</h2>

            <div class="receive-grid">
                <!-- First Row - 3 Cards -->
                <div class="receive-card certificate">
                    <div class="card-content">
                        <h3>Certificate Completion</h3>
                        <p>Reduce manual work with AI-powered scheduling</p>
                        <div class="card-icon">
                            <img src="./asserts/complete cirtificate.svg" alt="Certificate">
                        </div>
                    </div>
                </div>

                <div class="receive-card roadmap">
                    <div class="card-content">
                        <h3>UX Starter Roadmap</h3>
                        <p>Streamline clinic operations with intelligent automation</p>
                        <div class="card-icon">
                            <img src="./asserts/roadmap.svg" alt="Roadmap">
                        </div>
                    </div>
                </div>

                <div class="receive-card portfolio">
                    <div class="card-content">
                        <h3>Mini Project & Portfolio Tips</h3>
                        <p>Send timely reminders via preferred channels</p>
                        <div class="card-icon">
                            <img src="./asserts/portifolio.svg" alt="Portfolio">
                        </div>
                    </div>
                </div>

                <!-- Second Row - 2 Cards -->
                <div class="receive-card resource">
                    <div class="card-content">
                        <h3>Full Resource Kit</h3>
                        <p>AI-driven personalized patient engagement</p>
                        <div class="card-icon">
                            <img src="./asserts/resource kit.svg" alt="Resource Kit">
                        </div>
                    </div>
                </div>

                <div class="receive-card whatsapp">
                    <div class="card-content">
                        <h3>Invite to Private WhatsApp Group for Designers</h3>
                        <p>Send timely reminders via preferred channels</p>
                        <div class="card-icon">
                            <img src="./asserts/whatsapp.svg" alt="WhatsApp">
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Meet Your Mentor Section -->
    <section id="about" class="mentor-section">
        <div class="container">
            <div class="mentor-content">
                <!-- Header -->
                <div class="mentor-header">
                    <h2>Meet <span class="mentor-highlight">Your Mentor</span></h2>
                </div>

                <!-- Mentor Introduction -->
                <div class="mentor-intro">
                    <p class="mentor-greeting">
                        Hi, I'm
                        <img src="asserts/mentor.svg" alt="Sandeep" class="mentor-avatar-inline">
                        Sandeep – turning caffeine
                        <img src="asserts/coffee.svg" alt="Coffee" class="coffee-icon">
                        into digital products, brands, and experiences since
                        <span class="year-badge">2019</span>
                    </p>
                </div>

                <!-- Mentor Description -->
                <div class="mentor-description">
                    <p>I've helped <strong>1,000+ students</strong> start their design careers, spoken at design
                        workshops, and focused on simplifying design for beginners. My goal is to make good design
                        accessible and faster — with the help of AI.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Testimonials Section -->
    <section class="testimonials-section">
        <div class="container">
            <!-- Left side text -->
            <div class="testimonials-sidebar">
                <span class="testimonials-label">TESTIMONIALS</span>
            </div>

            <!-- Star decorations -->
            <div class="star-decoration star-top">
                <img src="./asserts/star-icon.svg" alt="Star" class="star-icon-svg">
            </div>
            <div class="star-decoration star-bottom">
                <img src="./asserts/star-icon.svg" alt="Star" class="star-icon-svg">
            </div>

            <!-- Main content -->
            <div class="testimonials-content">
                <div class="testimonials-header text-center">
                    <h2>What Our Learners Say</h2>
                    <p>Read through our testimonials to see why our clients love working with us<br>
                        and how we can help you achieve your business goals through creative<br>
                        and effective design.</p>
                </div>

                <!-- Swiper Testimonials Carousel -->
                <div class="testimonials-swiper-container">
                    <div class="swiper testimonials-swiper">
                        <div class="swiper-wrapper">
                            <!-- Testimonial 1: Priya -->
                            <div class="swiper-slide">
                                <div class="testimonial-card">
                                    <div class="testimonial-author">
                                        <div class="author-avatar"></div>
                                        <div class="author-info">
                                            <h4>Priya Sharma</h4>
                                            <p>Marketing Manager</p>
                                        </div>
                                    </div>
                                    <p class="testimonial-text">"This UX masterclass completely transformed my understanding of design thinking. The hands-on approach with AI tools like Figma AI and Visily was incredible. I went from zero UX knowledge to creating my first prototype in just one session!"</p>
                                </div>
                            </div>

                            <!-- Testimonial 2: Arjun -->
                            <div class="swiper-slide">
                                <div class="testimonial-card">
                                    <div class="testimonial-author">
                                        <img src="./asserts/person.svg" alt="Arjun Patel" class="author-avatar">
                                        <div class="author-info">
                                            <h4>Arjun Patel</h4>
                                            <p>Software Developer</p>
                                        </div>
                                    </div>
                                    <p class="testimonial-text">"As a developer, I always struggled with design. This masterclass bridged that gap perfectly. The AI-powered tools demonstration was mind-blowing, and now I can create beautiful interfaces with confidence. Worth every minute!"</p>
                                </div>
                            </div>

                            <!-- Testimonial 3: Sneha -->
                            <div class="swiper-slide">
                                <div class="testimonial-card">
                                    <div class="testimonial-author">
                                        <div class="author-avatar"></div>
                                        <div class="author-info">
                                            <h4>Sneha Reddy</h4>
                                            <p>Product Manager</p>
                                        </div>
                                    </div>
                                    <p class="testimonial-text">"The instructor's expertise in UX and AI tools is phenomenal. I learned more in this 2-hour session than months of online tutorials. The live prototype building was the highlight - seeing ideas come to life instantly was magical!"</p>
                                </div>
                            </div>

                            <!-- Testimonial 4: Rohit -->
                            <div class="swiper-slide">
                                <div class="testimonial-card">
                                    <div class="testimonial-author">
                                        <div class="author-avatar"></div>
                                        <div class="author-info">
                                            <h4>Rohit Gupta</h4>
                                            <p>Startup Founder</p>
                                        </div>
                                    </div>
                                    <p class="testimonial-text">"This masterclass gave me the UX foundation I needed for my startup. The AI tools like Lovable and Visily are game-changers. I can now validate my product ideas with real prototypes. Highly recommend to all entrepreneurs!"</p>
                                </div>
                            </div>

                            <!-- Testimonial 5: Kavya -->
                            <div class="swiper-slide">
                                <div class="testimonial-card">
                                    <div class="testimonial-author">
                                        <div class="author-avatar"></div>
                                        <div class="author-info">
                                            <h4>Kavya Singh</h4>
                                            <p>Design Student</p>
                                        </div>
                                    </div>
                                    <p class="testimonial-text">"Perfect introduction to UX design! The way complex concepts were simplified and the hands-on experience with 7+ AI tools was amazing. I now have a clear roadmap for my UX career. The free resources and templates are incredibly valuable!"</p>
                                </div>
                            </div>

                            <!-- Testimonial 6: Vikram -->
                            <div class="swiper-slide">
                                <div class="testimonial-card">
                                    <div class="testimonial-author">
                                        <div class="author-avatar"></div>
                                        <div class="author-info">
                                            <h4>Vikram Joshi</h4>
                                            <p>Business Analyst</p>
                                        </div>
                                    </div>
                                    <p class="testimonial-text">"Exceptional value for a free masterclass! The instructor's passion for UX and AI is contagious. I loved how we built a real prototype from scratch. The session opened my eyes to the future of design with AI assistance."</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Navigation -->
                    <div class="testimonials-navigation">
                        <button class="testimonial-nav-btn swiper-button-prev" aria-label="Previous testimonial">
                            <i class="fas fa-chevron-left"></i>
                        </button>

                        <!-- Pagination dots -->
                        <div class="swiper-pagination testimonials-pagination"></div>

                        <button class="testimonial-nav-btn swiper-button-next" aria-label="Next testimonial">
                            <i class="fas fa-chevron-right"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- FAQ Section -->
    <section id="faq" class="faq-section">
        <div class="container">
            <!-- Header with decorative text -->
            <div class="faq-header">
                <h2 class="decorative-title">
                    <span class="title-word">Innovate</span>
                    <img src="./asserts/Star 1.svg" alt="Star" class="star-separator">
                    <span class="title-word">Inspire</span>
                    <img src="./asserts/Star 1.svg" alt="Star" class="star-separator">
                    <span class="title-word">Create</span>
                </h2>
                <div class="title-divider">
                    <div class="divider-line"></div>
                    <img src="./asserts/Star 6.svg" alt="Star" class="divider-star">
                    <div class="divider-line"></div>
                </div>
            </div>

            <!-- FAQ Content -->
            <div class="faq-content">
                <div class="faq-left">
                    <div class="faq-badge">
                        <img src="./asserts/AI.svg" alt="AI" class="faq-icon">
                        <span>FAQ</span>
                    </div>
                    <h3>Common Questions About the Masterclass 🤔</h3>
                    <p>Explore our frequently asked questions to learn more about Masterclass Highlights.</p>
                </div>

                <div class="faq-right">
                    <div class="accordion" id="faqAccordion">
                        <div class="accordion-item faq-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button faq-question" type="button" data-bs-toggle="collapse"
                                    data-bs-target="#faq1" aria-expanded="true" aria-controls="faq1">
                                    <span class="question-number">I</span>
                                    <span class="question-text">Is this really completely free? 💰</span>
                                </button>
                            </h2>
                            <div id="faq1" class="accordion-collapse collapse show" data-bs-parent="#faqAccordion">
                                <div class="accordion-body faq-answer">
                                    <p>Yes, absolutely! This masterclass is 100% free with no hidden costs. We believe in making quality design education accessible to everyone.</p>
                                </div>
                            </div>
                        </div>

                        <div class="accordion-item faq-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed faq-question" type="button"
                                    data-bs-toggle="collapse" data-bs-target="#faq2" aria-expanded="false"
                                    aria-controls="faq2">
                                    <span class="question-number">I</span>
                                    <span class="question-text">I have zero design experience, can I still join? 👋</span>
                                </button>
                            </h2>
                            <div id="faq2" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                                <div class="accordion-body faq-answer">
                                    <p>Absolutely! This masterclass is designed specifically for complete beginners. No prior design experience or technical knowledge required.</p>
                                </div>
                            </div>
                        </div>

                        <div class="accordion-item faq-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed faq-question" type="button"
                                    data-bs-toggle="collapse" data-bs-target="#faq3" aria-expanded="false"
                                    aria-controls="faq3">
                                    <span class="question-number">I</span>
                                    <span class="question-text">How is this different from YouTube tutorials? 📚</span>
                                </button>
                            </h2>
                            <div id="faq3" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                                <div class="accordion-body faq-answer">
                                    <p>This is a live, interactive masterclass with real-time Q&A, hands-on exercises, and personalized guidance. You'll get direct access to industry experts and immediate feedback.</p>
                                </div>
                            </div>
                        </div>

                        <div class="accordion-item faq-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed faq-question" type="button"
                                    data-bs-toggle="collapse" data-bs-target="#faq4" aria-expanded="false"
                                    aria-controls="faq4">
                                    <span class="question-number">I</span>
                                    <span class="question-text">Will I really be able to find a job? 💼</span>
                                </button>
                            </h2>
                            <div id="faq4" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                                <div class="accordion-body faq-answer">
                                    <p>While we can't guarantee job placement, this masterclass will give you practical skills and portfolio pieces that employers value. Many of our students have successfully transitioned into UX roles.</p>
                                </div>
                            </div>
                        </div>

                        <div class="accordion-item faq-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed faq-question" type="button"
                                    data-bs-toggle="collapse" data-bs-target="#faq5" aria-expanded="false"
                                    aria-controls="faq5">
                                    <span class="question-number">I</span>
                                    <span class="question-text">What if I miss the live session? ⏰</span>
                                </button>
                            </h2>
                            <div id="faq5" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                                <div class="accordion-body faq-answer">
                                    <p>Don't worry! All registered participants will receive a recording of the session within 24 hours, so you can catch up at your own pace.</p>
                                </div>
                            </div>
                        </div>

                        <div class="accordion-item faq-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed faq-question" type="button"
                                    data-bs-toggle="collapse" data-bs-target="#faq6" aria-expanded="false"
                                    aria-controls="faq6">
                                    <span class="question-number">I</span>
                                    <span class="question-text">How long is the masterclass? ⏱️</span>
                                </button>
                            </h2>
                            <div id="faq6" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                                <div class="accordion-body faq-answer">
                                    <p>The masterclass is 2 hours long, packed with hands-on activities, live demonstrations, and interactive Q&A sessions to maximize your learning experience.</p>
                                </div>
                            </div>
                        </div>

                        <div class="accordion-item faq-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed faq-question" type="button"
                                    data-bs-toggle="collapse" data-bs-target="#faq7" aria-expanded="false"
                                    aria-controls="faq7">
                                    <span class="question-number">I</span>
                                    <span class="question-text">Will you share the recording? 📹</span>
                                </button>
                            </h2>
                            <div id="faq7" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                                <div class="accordion-body faq-answer">
                                    <p>Yes! All registered participants will receive access to the full recording, slides, and additional resources within 24 hours of the live session.</p>
                                </div>
                            </div>
                        </div>

                        <div class="accordion-item faq-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed faq-question" type="button"
                                    data-bs-toggle="collapse" data-bs-target="#faq8" aria-expanded="false"
                                    aria-controls="faq8">
                                    <span class="question-number">I</span>
                                    <span class="question-text">Is this suitable for career switchers affected by AI layoffs? 🤖</span>
                                </button>
                            </h2>
                            <div id="faq8" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                                <div class="accordion-body faq-answer">
                                    <p>Absolutely! This masterclass is perfect for professionals looking to pivot into UX design. We'll show you how to leverage AI tools to accelerate your transition and build a competitive portfolio.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    </main>

    <!-- Footer Section -->
    <footer class="footer-section">
        <div class="container-fluid">
            <div class="footer-content">
                <!-- Logo and Brand -->
                <div class="footer-brand">
                    <div class="footer-logo">
                        <img src="./asserts/logo.svg" alt="Katalyst Studio" class="footer-logo-img">
                        <div class="footer-brand-text">
                            <h3><span class="design">Design</span> Pirate</h3>
                            <p>Innovate. Inspire. Create</p>
                        </div>
                    </div>
                </div>

            </div>

            <!-- Copyright and Social Icons -->
            <div class="footer-copyright">
                <p>Copyright © 2025 Design Pirate. All rights reserved.</p>

                <!-- Social Media Icons -->
                <div class="footer-social">
                    <a href="#" class="social-link">
                        <i class="fab fa-facebook-f"></i>
                    </a>
                    <a href="#" class="social-link">
                        <i class="fab fa-linkedin-in"></i>
                    </a>
                    <a href="#" class="social-link">
                        <i class="fab fa-github"></i>
                    </a>
                    <a href="#" class="social-link">
                        <i class="fas fa-globe"></i>
                    </a>
                </div>
            </div>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.1/dist/js/bootstrap.bundle.min.js"
        integrity="sha384-HwwvtgBNo3bZJJLYd8oVXjrBZt8cqVSpeBNS5n7C8IVInixGAoxmnlMuBnhbgrkm"
        crossorigin="anonymous"></script>

    <!-- Swiper JS -->
    <script src="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.js"></script>

    <script>
        // Initialize Swiper for Testimonials
        document.addEventListener('DOMContentLoaded', function () {
            const testimonialsSwiper = new Swiper('.testimonials-swiper', {
                // Basic settings
                slidesPerView: 1,
                spaceBetween: 20,
                centeredSlides: true,
                loop: true,

                // Performance optimizations
                updateOnWindowResize: true,
                observer: true,
                observeParents: true,
                watchSlidesProgress: true,
                watchSlidesVisibility: true,

                // Responsive breakpoints
                breakpoints: {
                    // Mobile (default)
                    320: {
                        slidesPerView: 1,
                        spaceBetween: 20,
                        centeredSlides: true
                    },
                    // Tablet
                    768: {
                        slidesPerView: 2,
                        spaceBetween: 30,
                        centeredSlides: true
                    },
                    // Desktop
                    1024: {
                        slidesPerView: 3,
                        spaceBetween: 30,
                        centeredSlides: true
                    }
                },

                // Navigation
                navigation: {
                    nextEl: '.swiper-button-next',
                    prevEl: '.swiper-button-prev',
                    disabledClass: 'swiper-button-disabled',
                },

                // Pagination
                pagination: {
                    el: '.swiper-pagination',
                    clickable: true,
                    dynamicBullets: true,
                    dynamicMainBullets: 3,
                },

                // Smooth transitions
                effect: 'slide',
                speed: 400,
                longSwipesRatio: 0.1,
                longSwipesMs: 300,

                // Auto height
                autoHeight: false,

                // Keyboard control
                keyboard: {
                    enabled: true,
                    onlyInViewport: true,
                },

                // Optimized touch settings
                touchRatio: 1,
                touchAngle: 45,
                grabCursor: true,
                touchStartPreventDefault: false,
                touchMoveStopPropagation: false,
                simulateTouch: true,
                shortSwipes: true,
                longSwipes: true,

                // Accessibility
                a11y: {
                    prevSlideMessage: 'Previous testimonial',
                    nextSlideMessage: 'Next testimonial',
                },

                // Optimized events
                on: {
                    init: function () {
                        // Add custom classes for styling
                        this.slides.forEach((slide, index) => {
                            slide.classList.add('testimonial-slide');
                        });
                        // Force hardware acceleration
                        this.wrapperEl.style.transform3d = 'translate3d(0,0,0)';
                    },
                    slideChangeTransitionStart: function () {
                        // Optimize during transition
                        this.wrapperEl.style.willChange = 'transform';
                    },
                    slideChangeTransitionEnd: function () {
                        // Clean up after transition
                        this.wrapperEl.style.willChange = 'auto';
                    },
                    touchStart: function () {
                        // Prepare for smooth touch interaction
                        this.wrapperEl.style.willChange = 'transform';
                    },
                    touchEnd: function () {
                        // Clean up after touch
                        setTimeout(() => {
                            this.wrapperEl.style.willChange = 'auto';
                        }, 300);
                    }
                }
            });

            // Optional: Auto-play (uncomment if desired)
            // testimonialsSwiper.autoplay.start();
        });
    </script>



    <!-- Registration Modal -->
    <div id="registrationModal" class="modal-overlay" onclick="closeModal()">
        <div class="modal-container" onclick="event.stopPropagation()">
            <!-- Close Button -->
            <button class="modal-close" onclick="closeModal()">&times;</button>

            <!-- Modal Header -->
            <div class="modal-header">
                <h2>Register for UX Masterclass</h2>

                <!-- Progress Steps -->
                <div class="progress-steps">
                    <div class="step active" data-step="1">
                        <div class="step-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                        <span class="step-label">CHOOSE TIME</span>
                    </div>
                    <div class="step-line"></div>
                    <div class="step" data-step="2">
                        <div class="step-icon">
                            <i class="fas fa-user"></i>
                        </div>
                        <span class="step-label">YOUR INFO</span>
                    </div>
                    <div class="step-line"></div>
                    <div class="step" data-step="3">
                        <div class="step-icon">
                            <i class="fas fa-check"></i>
                        </div>
                        <span class="step-label">SUCCESS</span>
                    </div>
                </div>
            </div>

            <!-- Modal Content -->
            <div class="modal-content">
                <!-- Left Side - Calendar -->
                <div class="calendar-section">
                    <div class="event-card">
                        <div class="event-logo">
                            <span class="ux-logo">UX</span>
                        </div>
                        <h3>UX Academy Call</h3>
                        <div class="event-location">
                            <span class="location-icon">📍</span>
                            Live on Teams
                        </div>
                    </div>

                    <div class="calendar-widget">
                        <div class="calendar-navigation">
                            <button class="nav-btn prev-btn" onclick="navigateMonth(-1)">‹</button>
                            <div class="calendar-header" id="calendarHeader">Loading...</div>
                            <button class="nav-btn next-btn" onclick="navigateMonth(1)">›</button>
                        </div>
                        <div class="calendar-grid">
                            <div class="calendar-days">
                                <span>SUN</span><span>MON</span><span>TUE</span><span>WED</span><span>THU</span><span>FRI</span><span>SAT</span>
                            </div>
                            <div class="calendar-dates" id="calendarDates">
                                <!-- Calendar dates will be generated dynamically -->
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Right Side - Time Selection -->
                <div class="time-section">
                    <div class="meeting-duration">
                        <h4>Meeting duration</h4>
                        <div class="duration-info">
                            <div class="duration-icon">🕐</div>
                            <span>120 mins</span>
                        </div>
                    </div>

                    <div class="time-selection">
                        <h4>What time works best?</h4>
                        <p class="time-subtitle" id="timeSubtitle">Showing times for today</p>

                        <div class="timezone-selector">
                            <select>
                                <option>UTC +05:30 New Delhi, Mumbai, Calcutta</option>
                            </select>
                        </div>

                        <div class="time-slots" id="timeSlots">
                            <div class="time-slot active selected" data-time="6:00 PM IST">
                                <div class="time-icon">🕐</div>
                                <span>6:00 PM IST</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Info Form Content (hidden initially) -->
                <div class="info-content" id="infoContent" style="display: none;">
                    <!-- Selected Time Display -->
                    <div class="selected-time-display">
                        <span class="calendar-icon">📅</span>
                        <span id="selectedDate">Selected Date</span>
                        <span class="time-icon">🕐</span>
                        <span id="selectedTime">6:00 PM IST</span>
                    </div>

                    <!-- Registration Form -->
                    <form class="registration-form">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="firstName">
                                    First Name *
                                </label>
                                <input type="text" id="firstName" name="firstName" placeholder="Enter your first name" required>
                            </div>
                            <div class="form-group">
                                <label for="lastName">
                                    Last Name *
                                </label>
                                <input type="text" id="lastName" name="lastName" placeholder="Enter your last name" required>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="email">
                                Email Address *
                            </label>
                            <input type="email" id="email" name="email" placeholder="Enter your email address" required>
                        </div>

                        <div class="form-group">
                            <label for="phone">
                                Phone Number
                            </label>
                            <input type="tel" id="phone" name="phone" placeholder="Enter your phone number">
                        </div>

                        <div class="form-group">
                            <label for="experience">
                                Experience Level
                            </label>
                            <select id="experience" name="experience" required>
                                <option value="">Select your experience level</option>
                                <option value="beginner">Beginner</option>
                                <option value="intermediate">Intermediate</option>
                                <option value="advanced">Advanced</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="hearAbout">How did you hear about us?</label>
                            <select id="hearAbout" name="hearAbout">
                                <option value="">Select an option</option>
                                <option value="social-media">Social Media</option>
                                <option value="google">Google Search</option>
                                <option value="friend">Friend/Colleague</option>
                                <option value="newsletter">Newsletter</option>
                                <option value="other">Other</option>
                            </select>
                        </div>

                        <div class="form-group checkbox-group">
                            <label class="checkbox-label">
                                <input type="checkbox" id="terms" name="terms" required>
                                <span class="checkmark"></span>
                                I agree to the Terms of Service and Privacy Policy *
                            </label>
                        </div>

                        <div class="form-group checkbox-group">
                            <label class="checkbox-label">
                                <input type="checkbox" id="updates" name="updates">
                                <span class="checkmark"></span>
                                I would like to receive updates about future masterclasses and UX resources
                            </label>
                        </div>

                        <div class="form-actions">
                            <button type="button" class="back-btn" onclick="showTimeSelection()">
                                ← Back
                            </button>
                            <button type="submit" class="submit-btn" id="submitBtn" disabled>
                                <span class="btn-text">Complete Registration</span>
                                <span class="btn-loader" style="display: none;">
                                    <i class="fas fa-spinner fa-spin"></i> Submitting...
                                </span>
                            </button>
                        </div>
                    </form>
                </div>

                <!-- Success Screen Content (hidden initially) -->
                <div class="success-content" id="successContent" style="display: none;">
                    <!-- Success Icon and Message -->
                    <div class="success-header">
                        <div class="success-icon">
                            <i class="fas fa-check-circle animated-check"></i>
                        </div>
                        <h2 class="success-title">Registration Successful!</h2>
                        <p class="success-message">You're all set for the UX Masterclass. We've sent a confirmation
                            email with all the details.</p>
                    </div>

                    <!-- Masterclass Details Card -->
                    <div class="masterclass-details-card">
                        <h3>Your Masterclass Details</h3>
                        <div class="detail-item">
                            <span class="detail-icon">📅</span>
                            <span id="successDate">June 21, 2025</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-icon">🕐</span>
                            <span id="successTime">1:00 PM IST</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-icon">📍</span>
                            <span>Live on Teams</span>
                        </div>
                    </div>

                    <!-- What happens next section -->
                    <div class="next-steps-card">
                        <h3>What happens next?</h3>
                        <div class="step-item">
                            <span class="step-number">1</span>
                            <div class="step-content">
                                <strong>Check your email</strong>
                                <p>You'll receive a confirmation email with the Teams link and calendar invite.</p>
                            </div>
                        </div>
                        <div class="step-item">
                            <span class="step-number">2</span>
                            <div class="step-content">
                                <strong>Join our WhatsApp group</strong>
                                <p>Get updates, reminders, and connect with other participants.</p>
                            </div>
                        </div>
                        <div class="step-item">
                            <span class="step-number">3</span>
                            <div class="step-content">
                                <strong>Prepare for the session</strong>
                                <p>No software installation needed. Just bring your enthusiasm to learn!</p>
                            </div>
                        </div>
                    </div>

                    <!-- Action buttons -->
                    <div class="success-actions">
                        <button type="button" class="calendar-btn" onclick="addToCalendar()">
                            📅 Add to Calendar
                        </button>
                        <button type="button" class="whatsapp-btn" onclick="joinWhatsAppGroup()">
                            💬 Join WhatsApp Group
                        </button>
                        <button type="button" class="done-btn" onclick="closeModal()">
                            Done
                        </button>
                    </div>

                    <!-- Help text -->
                    <div class="help-text">
                        <p><strong>Need help?</strong> Contact <NAME_EMAIL> or call +91 9779022133</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Roadmap Download Modal -->
    <div id="roadmapModal" class="modal-overlay" onclick="closeRoadmapModal()" style="display: none;">
        <div class="roadmap-modal-container" onclick="event.stopPropagation()">
            <!-- Close Button -->
            <button class="modal-close" onclick="closeRoadmapModal()">&times;</button>

            <!-- Modal Content -->
            <div class="roadmap-modal-content">
                <!-- Header -->
                <div class="roadmap-modal-header">
                    <div class="roadmap-icon">
                        <i class="fas fa-route"></i>
                    </div>
                    <h2>Download UX Roadmap</h2>
                    <p>Get your comprehensive UX learning roadmap and start your journey to becoming a UX expert!</p>
                </div>

                <!-- Form -->
                <form class="roadmap-form" id="roadmapForm">
                    <div class="form-group">
                        <label for="roadmapName">
                            <i class="fas fa-user"></i>
                            Full Name *
                        </label>
                        <input type="text" id="roadmapName" name="name" placeholder="Enter your full name" required>
                    </div>

                    <div class="form-group">
                        <label for="roadmapEmail">
                            <i class="fas fa-envelope"></i>
                            Email Address *
                        </label>
                        <input type="email" id="roadmapEmail" name="email" placeholder="Enter your email address" required>
                    </div>

                    <div class="form-group">
                        <label for="roadmapPhone">
                            <i class="fas fa-phone"></i>
                            Phone Number *
                        </label>
                        <input type="tel" id="roadmapPhone" name="phone" placeholder="Enter your phone number" required>
                    </div>

                    <div class="form-group checkbox-group">
                        <label class="checkbox-label">
                            <input type="checkbox" id="roadmapUpdates" name="updates">
                            <span class="checkmark"></span>
                            I would like to receive updates about UX resources and courses
                        </label>
                    </div>

                    <!-- Download Button -->
                    <button type="submit" class="roadmap-download-btn" id="roadmapDownloadBtn">
                        <span class="btn-text">
                            <i class="fas fa-download"></i>
                            Download Roadmap
                        </span>
                        <span class="btn-loader" style="display: none;">
                            <i class="fas fa-spinner fa-spin"></i> Preparing Download...
                        </span>
                    </button>
                </form>

                <!-- Features List -->
                <div class="roadmap-features">
                    <h4>What's included:</h4>
                    <ul>
                        <li><i class="fas fa-check"></i> Complete UX learning path</li>
                        <li><i class="fas fa-check"></i> Essential tools and resources</li>
                        <li><i class="fas fa-check"></i> Career guidance and tips</li>
                        <li><i class="fas fa-check"></i> Industry best practices</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>



    <script>
        function openModal() {
            console.log('Opening modal - resetting to initial state');

            try {
                // Reset modal to initial state
                resetModalToInitialState();

                // Ensure modal starts with time selection size
                const modalContainer = document.querySelector('.modal-container');
                if (modalContainer) {
                    modalContainer.classList.remove('form-step');
                }

                document.getElementById('registrationModal').style.display = 'flex';
                document.body.style.overflow = 'hidden';
                document.body.classList.add('modal-open');
                console.log('Modal opened successfully');
            } catch (error) {
                console.error('Error opening modal:', error);
                // Fallback - just open the modal without reset
                document.getElementById('registrationModal').style.display = 'flex';
                document.body.style.overflow = 'hidden';
                document.body.classList.add('modal-open');
            }
        }

        function resetModalToInitialState() {
            console.log('Resetting modal to initial state');

            try {
                // Show calendar section and time section (initial layout)
                const calendarSection = document.querySelector('.calendar-section');
                const timeSection = document.querySelector('.time-section');
                const infoContent = document.querySelector('.info-content');
                const successContent = document.getElementById('successContent');

                console.log('Calendar section found:', !!calendarSection);
                console.log('Time section found:', !!timeSection);
                console.log('Info content found:', !!infoContent);
                console.log('Success content found:', !!successContent);

                // Show initial sections (calendar + time selection)
                if (calendarSection) {
                    calendarSection.style.display = 'flex';
                }

                if (timeSection) {
                    timeSection.style.display = 'flex';
                }

                // Hide form and success sections
                if (infoContent) {
                    infoContent.style.display = 'none';
                }

                if (successContent) {
                    successContent.style.display = 'none';
                }

                // Reset progress indicators
                resetProgressIndicators();

                // Clear form data
                const form = document.querySelector('.registration-form');
                if (form) {
                    form.reset();
                }

                // Clear selected time slots
                const timeSlots = document.querySelectorAll('.time-slot');
                timeSlots.forEach(slot => {
                    slot.classList.remove('active', 'selected');
                });

                // Reinitialize calendar
                initializeCalendar();

                console.log('Modal reset completed successfully');
            } catch (error) {
                console.error('Error resetting modal:', error);
            }
        }

        function resetProgressIndicators() {
            try {
                const steps = document.querySelectorAll('.step');
                console.log('Found steps:', steps.length);

                steps.forEach((step, index) => {
                    if (step) {
                        if (index === 0) { // First step (Choose Time) - active
                            step.classList.add('active');
                            step.classList.remove('completed');
                        } else { // Other steps - inactive
                            step.classList.remove('active', 'completed');
                        }
                    }
                });
            } catch (error) {
                console.error('Error resetting progress indicators:', error);
            }
        }

        // Step progression functions
        function updateStepProgress(currentStep) {
            const steps = document.querySelectorAll('.step');

            steps.forEach((step, index) => {
                const stepNumber = index + 1;

                if (stepNumber < currentStep) {
                    // Completed steps
                    step.classList.add('completed');
                    step.classList.remove('active');
                } else if (stepNumber === currentStep) {
                    // Current active step
                    step.classList.add('active');
                    step.classList.remove('completed');
                } else {
                    // Future steps
                    step.classList.remove('active', 'completed');
                }
            });
        }

        function goToStep(stepNumber) {
            // Update progress indicators
            updateStepProgress(stepNumber);

            // Show/hide appropriate content
            const calendarSection = document.querySelector('.calendar-section');
            const timeSection = document.querySelector('.time-section');
            const infoContent = document.querySelector('.info-content');
            const successContent = document.getElementById('successContent');
            const modalContainer = document.querySelector('.modal-container');

            // Hide all sections first
            if (calendarSection) calendarSection.style.display = 'none';
            if (timeSection) timeSection.style.display = 'none';
            if (infoContent) infoContent.style.display = 'none';
            if (successContent) successContent.style.display = 'none';

            switch(stepNumber) {
                case 1: // Choose Time
                    if (calendarSection) calendarSection.style.display = 'flex';
                    if (timeSection) timeSection.style.display = 'flex';
                    if (modalContainer) modalContainer.classList.remove('form-step');
                    break;
                case 2: // Your Info
                    if (infoContent) infoContent.style.display = 'block';
                    if (modalContainer) modalContainer.classList.add('form-step');
                    break;
                case 3: // Success
                    if (successContent) successContent.style.display = 'block';
                    if (modalContainer) modalContainer.classList.add('form-step');
                    break;
            }
        }

        function closeModal() {
            document.getElementById('registrationModal').style.display = 'none';
            document.body.style.overflow = 'auto';
            document.body.classList.remove('modal-open');
        }

        // Roadmap Modal Functions
        function openRoadmapModal() {
            document.getElementById('roadmapModal').style.display = 'flex';
            document.body.style.overflow = 'hidden';
            document.body.classList.add('modal-open');

            // Reset form
            const form = document.getElementById('roadmapForm');
            if (form) {
                form.reset();
                validateRoadmapForm(); // Initial validation
            }
        }

        function closeRoadmapModal() {
            document.getElementById('roadmapModal').style.display = 'none';
            document.body.style.overflow = 'auto';
            document.body.classList.remove('modal-open');
        }

        // Roadmap form validation
        function validateRoadmapForm() {
            const form = document.getElementById('roadmapForm');
            const downloadBtn = document.getElementById('roadmapDownloadBtn');

            if (!form || !downloadBtn) return;

            const name = form.querySelector('#roadmapName').value.trim();
            const email = form.querySelector('#roadmapEmail').value.trim();
            const phone = form.querySelector('#roadmapPhone').value.trim();

            const isValid = name && email && phone;

            downloadBtn.disabled = !isValid;
            downloadBtn.classList.toggle('enabled', isValid);
        }

        // Handle roadmap form submission
        async function handleRoadmapDownload(e) {
            e.preventDefault();

            const form = document.getElementById('roadmapForm');
            const downloadBtn = document.getElementById('roadmapDownloadBtn');
            const btnText = downloadBtn.querySelector('.btn-text');
            const btnLoader = downloadBtn.querySelector('.btn-loader');

            // Show loading state
            btnText.style.display = 'none';
            btnLoader.style.display = 'inline-flex';
            downloadBtn.disabled = true;

            try {
                // Get form data
                const formData = new FormData(form);
                const roadmapData = {
                    name: formData.get('name') || '',
                    email: formData.get('email') || '',
                    phone: formData.get('phone') || '',
                    receiveUpdates: formData.get('updates') === 'on',
                };

                // Validate required fields
                if (!roadmapData.name || !roadmapData.email || !roadmapData.phone) {
                    throw new Error('Please fill in all required fields');
                }

                // Submit to Firebase (roadmaps collection)
                if (window.firebaseDb && window.firebaseCollection && window.firebaseAddDoc && window.firebaseServerTimestamp) {
                    const docRef = await window.firebaseAddDoc(window.firebaseCollection(window.firebaseDb, "roadmaps"), {
                        ...roadmapData,
                        createdAt: window.firebaseServerTimestamp(),
                        status: 'downloaded'
                    });
                    console.log("Roadmap download tracked with ID: ", docRef.id);
                }

                // Track analytics event
                if (window.trackMasterclassEvent) {
                    window.trackMasterclassEvent('roadmap_download', {
                        user_name: roadmapData.name,
                        user_email: roadmapData.email
                    });
                }

                // Start download
                const link = document.createElement('a');
                link.href = roadmapData.downloadUrl;
                link.download = 'UX-Roadmap-HashInclude.pdf';
                link.target = '_blank';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);

                // Show success message
                alert('Thank you! Your UX Roadmap download has started. Check your downloads folder.');

                // Close modal
                closeRoadmapModal();

            } catch (error) {
                console.error('Roadmap download error:', error);
                alert('Download failed: ' + error.message + '. Please try again.');

                // Reset button state
                btnText.style.display = 'inline-flex';
                btnLoader.style.display = 'none';
                validateRoadmapForm(); // Re-enable based on form validation
            }
        }

        // Calendar functionality
        let selectedDate = null;
        let currentMonth = new Date().getMonth();
        let currentYear = new Date().getFullYear();

        function generateCalendar(month, year) {
            const calendarDates = document.getElementById('calendarDates');
            const calendarHeader = document.getElementById('calendarHeader');

            if (!calendarDates || !calendarHeader) return;

            const monthNames = [
                'January', 'February', 'March', 'April', 'May', 'June',
                'July', 'August', 'September', 'October', 'November', 'December'
            ];

            calendarHeader.textContent = `${monthNames[month]} ${year}`;

            // Update navigation buttons
            const prevBtn = document.querySelector('.prev-btn');
            const nextBtn = document.querySelector('.next-btn');
            const todayDate = new Date();
            const currentMonthDate = new Date(year, month, 1);
            const todayMonthDate = new Date(todayDate.getFullYear(), todayDate.getMonth(), 1);

            if (prevBtn) {
                prevBtn.disabled = currentMonthDate <= todayMonthDate;
            }

            const firstDay = new Date(year, month, 1).getDay();
            const daysInMonth = new Date(year, month + 1, 0).getDate();
            const today = todayDate;
            const currentDate = today.getDate();
            const currentMonthYear = today.getMonth() === month && today.getFullYear() === year;

            let calendarHTML = '';

            // Add empty cells for days before the first day of the month
            for (let i = 0; i < firstDay; i++) {
                calendarHTML += '<span></span>';
            }

            // Add days of the month
            for (let day = 1; day <= daysInMonth; day++) {
                const dateObj = new Date(year, month, day);
                const todayObj = new Date();
                todayObj.setHours(0, 0, 0, 0); // Reset time to compare only dates

                const isPast = dateObj < todayObj;
                const isToday = currentMonthYear && day === currentDate;
                const isSelected = selectedDate && selectedDate.getDate() === day &&
                    selectedDate.getMonth() === month && selectedDate.getFullYear() === year;

                // Check if the day is Tuesday (2) or Friday (5)
                const dayOfWeek = dateObj.getDay(); // 0 = Sunday, 1 = Monday, 2 = Tuesday, etc.
                const isAvailableDay = dayOfWeek === 2 || dayOfWeek === 5; // Tuesday or Friday

                let classes = [];
                if (isPast || !isAvailableDay) {
                    classes.push('disabled');
                } else {
                    classes.push('enabled');
                    classes.push('available'); // Add special class for available days
                }
                if (isToday) classes.push('today');
                if (isSelected) classes.push('selected');

                const classStr = classes.length > 0 ? ` class="${classes.join(' ')}"` : '';
                const dataDate = `${year}-${String(month + 1).padStart(2, '0')}-${String(day).padStart(2, '0')}`;

                // Only allow clicking on available days that are not in the past
                const clickHandler = (isPast || !isAvailableDay) ? '' : 'onclick="selectDate(this)"';
                calendarHTML += `<span${classStr} data-date="${dataDate}" ${clickHandler}>${day}</span>`;
            }

            calendarDates.innerHTML = calendarHTML;
        }

        function selectDate(element) {
            if (element.classList.contains('disabled')) {
                console.log('Date is disabled, cannot select');
                return;
            }

            console.log('Selecting date:', element.textContent);

            // Remove previous selection
            const previousSelected = document.querySelector('.calendar-dates span.selected');
            if (previousSelected) {
                previousSelected.classList.remove('selected');
                console.log('Removed previous selection from:', previousSelected.textContent);
            }

            // Add selection to clicked date
            element.classList.add('selected');
            console.log('Added selection to:', element.textContent);
            console.log('Element classes after selection:', element.className);

            // Store selected date
            const dateStr = element.getAttribute('data-date');
            selectedDate = new Date(dateStr);

            // Update time section subtitle with selected date
            updateTimeSubtitle(dateStr);

            // Update time slots for selected date
            updateTimeSlotsForDate(dateStr);

            console.log('Selected date:', dateStr);
        }

        function updateTimeSubtitle(dateStr) {
            const timeSubtitle = document.getElementById('timeSubtitle');
            if (!timeSubtitle) return;

            const selectedDateObj = new Date(dateStr);
            const monthNames = [
                'January', 'February', 'March', 'April', 'May', 'June',
                'July', 'August', 'September', 'October', 'November', 'December'
            ];

            const month = monthNames[selectedDateObj.getMonth()];
            const day = selectedDateObj.getDate();
            const year = selectedDateObj.getFullYear();

            timeSubtitle.textContent = `Showing times for ${month} ${day}, ${year}`;
        }

        function updateTimeSlotsForDate(dateStr) {
            const timeSlotsContainer = document.getElementById('timeSlots');
            if (!timeSlotsContainer) return;

            const selectedDateObj = new Date(dateStr);
            const dayOfWeek = selectedDateObj.getDay(); // 0 = Sunday, 1 = Monday, etc.

            // Only show 6:00 PM for available days (Tuesday = 2, Friday = 5)
            if (dayOfWeek === 2 || dayOfWeek === 5) {
                // Format the date for display
                const monthNames = [
                    'January', 'February', 'March', 'April', 'May', 'June',
                    'July', 'August', 'September', 'October', 'November', 'December'
                ];
                const month = monthNames[selectedDateObj.getMonth()];
                const day = selectedDateObj.getDate();
                const year = selectedDateObj.getFullYear();
                const formattedDate = `${month} ${day}, ${year}`;

                // Update time slots container with single 6:00 PM slot
                timeSlotsContainer.innerHTML = `
                    <div class="time-slot active selected" data-time="6:00 PM IST" data-date="${formattedDate}">
                        <div class="time-icon">🕐</div>
                        <span>6:00 PM IST</span>
                    </div>
                `;

                // Re-attach click listeners after updating time slots
                if (window.attachTimeSlotListeners) {
                    console.log('Re-attaching time slot listeners after update');
                    window.attachTimeSlotListeners();
                }
            } else {
                // This shouldn't happen since only Tuesdays and Fridays are selectable
                timeSlotsContainer.innerHTML = '<p>No available times for this date.</p>';
            }
        }

        // Navigate between months
        function navigateMonth(direction) {
            currentMonth += direction;

            if (currentMonth > 11) {
                currentMonth = 0;
                currentYear++;
            } else if (currentMonth < 0) {
                currentMonth = 11;
                currentYear--;
            }

            // Don't allow navigation to past months
            const today = new Date();
            const currentMonthDate = new Date(currentYear, currentMonth, 1);
            const todayMonthDate = new Date(today.getFullYear(), today.getMonth(), 1);

            if (currentMonthDate < todayMonthDate) {
                // Reset to current month if trying to go to past
                currentMonth = today.getMonth();
                currentYear = today.getFullYear();
                return;
            }

            generateCalendar(currentMonth, currentYear);

            // Auto-select first available date in new month (Tuesday or Friday)
            setTimeout(() => {
                const availableDates = document.querySelectorAll('.calendar-dates span.available');
                if (availableDates.length > 0) {
                    selectDate(availableDates[0]);
                }
            }, 100);
        }

        // Initialize calendar when page loads
        function initializeCalendar() {
            // Use current date instead of hardcoded June 2025
            const today = new Date();
            currentMonth = today.getMonth(); // Current month (0-indexed)
            currentYear = today.getFullYear(); // Current year
            generateCalendar(currentMonth, currentYear);

            // Auto-select first available date (Tuesday or Friday)
            setTimeout(() => {
                const availableDates = document.querySelectorAll('.calendar-dates span.available');
                if (availableDates.length > 0) {
                    // Try to select today if it's available (Tuesday or Friday)
                    const todayDate = today.getDate();
                    const todaySpan = Array.from(availableDates).find(span =>
                        span.textContent === todayDate.toString() && span.classList.contains('today')
                    );

                    if (todaySpan) {
                        selectDate(todaySpan);
                    } else {
                        // Select first available Tuesday or Friday
                        selectDate(availableDates[0]);
                    }
                } else {
                    // If no available dates in current month, try next month
                    navigateMonth(1);
                }
            }, 100);
        }

        // Form validation and button enabling
        function validateForm() {
            const form = document.querySelector('.registration-form');
            const submitBtn = document.getElementById('submitBtn');

            if (!form || !submitBtn) return;

            const firstName = form.querySelector('#firstName').value.trim();
            const lastName = form.querySelector('#lastName').value.trim();
            const email = form.querySelector('#email').value.trim();
            const experience = form.querySelector('#experience').value;
            const terms = form.querySelector('#terms').checked;

            const isValid = firstName && lastName && email && experience && terms;

            submitBtn.disabled = !isValid;
            submitBtn.classList.toggle('enabled', isValid);
        }

        // Time slot selection and info form
        document.addEventListener('DOMContentLoaded', function () {
            // Initialize calendar
            initializeCalendar();

            // Add form validation listeners
            const form = document.querySelector('.registration-form');
            if (form) {
                const inputs = form.querySelectorAll('input[required], select[required]');
                const termsCheckbox = form.querySelector('#terms');

                inputs.forEach(input => {
                    input.addEventListener('input', validateForm);
                    input.addEventListener('change', validateForm);
                });

                if (termsCheckbox) {
                    termsCheckbox.addEventListener('change', validateForm);
                }

                // Initial validation
                validateForm();
            }

            // Add roadmap form validation listeners
            const roadmapForm = document.getElementById('roadmapForm');
            if (roadmapForm) {
                const roadmapInputs = roadmapForm.querySelectorAll('input[required]');

                roadmapInputs.forEach(input => {
                    input.addEventListener('input', validateRoadmapForm);
                    input.addEventListener('change', validateRoadmapForm);
                });

                // Add form submission listener
                roadmapForm.addEventListener('submit', handleRoadmapDownload);

                // Initial validation
                validateRoadmapForm();
            }

            // Add click listeners to all time slots (both existing and dynamically created)
            function attachTimeSlotListeners() {
                const timeSlots = document.querySelectorAll('.time-slot');
                console.log('Attaching listeners to', timeSlots.length, 'time slots');

                timeSlots.forEach((slot, index) => {
                    // Remove any existing listeners to prevent duplicates
                    slot.removeEventListener('click', handleTimeSlotClick);
                    // Add the click listener
                    slot.addEventListener('click', handleTimeSlotClick);
                    console.log('Added listener to time slot', index, ':', slot.textContent.trim());
                });
            }

            // Handle time slot click
            function handleTimeSlotClick(e) {
                console.log('=== TIME SLOT CLICKED ===');
                const clickedSlot = e.currentTarget;
                console.log('Clicked slot:', clickedSlot.textContent.trim());

                // Prevent any default behavior
                e.preventDefault();
                e.stopPropagation();

                // Remove previous selections
                const allTimeSlots = document.querySelectorAll('.time-slot');
                allTimeSlots.forEach(s => {
                    s.classList.remove('active', 'selected');
                });

                // Add selection to clicked slot
                clickedSlot.classList.add('active', 'selected');
                console.log('Selected time slot:', clickedSlot.textContent.trim());

                // Get selected time and date
                let selectedTime = clickedSlot.getAttribute('data-time');
                let selectedDateText = clickedSlot.getAttribute('data-date');

                if (!selectedTime) {
                    const spanElement = clickedSlot.querySelector('span');
                    selectedTime = spanElement ? spanElement.textContent : clickedSlot.textContent.trim();
                }

                console.log('Selected time:', selectedTime);
                console.log('Selected date:', selectedDateText);

                // Update selected time and date display in form
                const selectedTimeElement = document.getElementById('selectedTime');
                const selectedDateElement = document.getElementById('selectedDate');

                if (selectedTimeElement) {
                    selectedTimeElement.textContent = selectedTime;
                }

                if (selectedDateElement && selectedDateText) {
                    selectedDateElement.textContent = selectedDateText;
                }

                // Show registration form immediately
                console.log('Calling showInfoForm()');
                showInfoForm();
            }

            // Initial attachment
            attachTimeSlotListeners();

            // Re-attach listeners when time slots are updated
            window.attachTimeSlotListeners = attachTimeSlotListeners;
        });

        // Show info form within the same modal
        function showInfoForm() {
            console.log('showInfoForm called - transitioning to registration form');
            goToStep(2); // Use new step progression system
        }

        // Show time selection (back button)
        function showTimeSelection() {
            console.log('showTimeSelection called - going back to step 1');
            goToStep(1); // Use new step progression system
        }



        // Form submission
        document.addEventListener('DOMContentLoaded', function () {
            const form = document.querySelector('.registration-form');
            console.log('Form found:', !!form);

            if (form) {
                form.addEventListener('submit', async function (e) {
                    console.log('Form submitted!');
                    e.preventDefault();

                    // Show loading state
                    const submitBtn = form.querySelector('.submit-btn');
                    const btnText = submitBtn.querySelector('.btn-text');
                    const btnLoader = submitBtn.querySelector('.btn-loader');

                    btnText.style.display = 'none';
                    btnLoader.style.display = 'inline-flex';
                    submitBtn.disabled = true;

                    try {
                        // Get form data
                        const formData = new FormData(form);
                        const selectedDate = document.querySelector('.time-slot.selected')?.dataset.date;
                        const selectedTime = document.querySelector('.time-slot.selected')?.dataset.time;

                        // Prepare registration data
                        const registrationData = {
                            firstName: formData.get('firstName') || '',
                            lastName: formData.get('lastName') || '',
                            email: formData.get('email') || '',
                            phone: formData.get('phone') || '',
                            experience: formData.get('experience') || '',
                            hearAbout: formData.get('hearAbout') || '',
                            selectedDate: selectedDate || '',
                            selectedTime: selectedTime || '6:00 PM IST',
                            agreeToTerms: formData.get('terms') === 'on',
                            receiveUpdates: formData.get('updates') === 'on',
                        };

                        console.log('Registration data:', registrationData);

                        // Validate required fields
                        if (!registrationData.firstName || !registrationData.lastName || !registrationData.email) {
                            throw new Error('Please fill in all required fields');
                        }

                        if (!registrationData.agreeToTerms) {
                            throw new Error('Please agree to the Terms of Service and Privacy Policy');
                        }

                        if (!selectedDate || !selectedTime) {
                            throw new Error('Please select a date and time');
                        }

                        // Submit to Firebase
                        const result = await window.submitRegistration(registrationData);

                        if (result.success) {
                            console.log('Registration successful:', result.id);

                            // Update success screen with registration details
                            document.getElementById('successDate').textContent = selectedDate;
                            document.getElementById('successTime').textContent = selectedTime;

                            // Show success screen
                            showSuccessScreen();
                        } else {
                            throw new Error(result.error || 'Registration failed');
                        }

                    } catch (error) {
                        console.error('Registration error:', error);
                        alert('Registration failed: ' + error.message + '. Please try again.');

                        // Reset button state
                        btnText.style.display = 'inline';
                        btnLoader.style.display = 'none';
                        validateForm(); // Re-enable based on form validation
                    }
                });
            } else {
                console.error('Registration form not found!');
            }
        });

        function showSuccessScreen() {
            console.log('showSuccessScreen called');
            goToStep(3); // Use new step progression system
        }

        // Success screen button handlers
        function addToCalendar() {
            const selectedDate = document.getElementById('successDate').textContent;
            const selectedTime = document.getElementById('successTime').textContent;

            // Create calendar event details
            const eventTitle = 'UX Masterclass - HashInclude';
            const eventDetails = `Join us for an exclusive UX Masterclass session!

📅 Date: ${selectedDate}
🕕 Time: ${selectedTime}
💻 Platform: Microsoft Teams
📧 Contact: <EMAIL>
📞 Phone: +91 9779022133

What to expect:
• Interactive UX design session
• Hands-on exercises
• Q&A with industry experts
• Networking opportunities

Teams link will be shared via email before the session.`;

            const location = 'Microsoft Teams (Online)';

            try {
                console.log('Selected date:', selectedDate);
                console.log('Selected time:', selectedTime);

                // Parse the date and time more reliably
                // Convert "July 18, 2025" format to a proper date
                let startDate;

                // Try to parse the date in different formats
                if (selectedDate.includes(',')) {
                    // Format: "July 18, 2025"
                    const dateParts = selectedDate.split(',');
                    const monthDay = dateParts[0].trim();
                    const year = dateParts[1].trim();

                    // Parse "July 18" part
                    const monthDayParts = monthDay.split(' ');
                    const monthName = monthDayParts[0];
                    const day = monthDayParts[1];

                    // Convert month name to number
                    const monthNames = ['January', 'February', 'March', 'April', 'May', 'June',
                                     'July', 'August', 'September', 'October', 'November', 'December'];
                    const monthIndex = monthNames.indexOf(monthName);

                    if (monthIndex !== -1) {
                        // Create date object
                        const timeStr = selectedTime.replace(' IST', '');
                        const dateTimeString = `${year}-${String(monthIndex + 1).padStart(2, '0')}-${String(day).padStart(2, '0')} ${timeStr}`;
                        startDate = new Date(dateTimeString);
                        console.log('Parsed date:', startDate);
                    }
                }

                // Fallback parsing
                if (!startDate || isNaN(startDate.getTime())) {
                    const dateTimeString = `${selectedDate} ${selectedTime.replace(' IST', '')}`;
                    startDate = new Date(dateTimeString);
                }

                // Final fallback - use current date if parsing fails
                if (!startDate || isNaN(startDate.getTime())) {
                    console.warn('Date parsing failed, using current date');
                    startDate = new Date();
                    startDate.setHours(18, 0, 0, 0); // Set to 6 PM
                }

                // Handle IST timezone (UTC+5:30)
                const istOffset = 5.5 * 60 * 60 * 1000; // 5.5 hours in milliseconds
                const utcStartDate = new Date(startDate.getTime() - istOffset);
                const utcEndDate = new Date(utcStartDate.getTime() + 2 * 60 * 60 * 1000); // 2 hours later

                console.log('Final start date:', startDate);
                console.log('UTC start date:', utcStartDate);

                // Format dates for calendar URL
                const formatDate = (date) => date.toISOString().replace(/[-:]/g, '').split('.')[0] + 'Z';

                // Create multiple calendar options
                const calendarOptions = {
                    google: `https://calendar.google.com/calendar/render?action=TEMPLATE&text=${encodeURIComponent(eventTitle)}&dates=${formatDate(utcStartDate)}/${formatDate(utcEndDate)}&details=${encodeURIComponent(eventDetails)}&location=${encodeURIComponent(location)}`,

                    outlook: `https://outlook.live.com/calendar/0/deeplink/compose?subject=${encodeURIComponent(eventTitle)}&startdt=${utcStartDate.toISOString()}&enddt=${utcEndDate.toISOString()}&body=${encodeURIComponent(eventDetails)}&location=${encodeURIComponent(location)}`,

                    yahoo: `https://calendar.yahoo.com/?v=60&view=d&type=20&title=${encodeURIComponent(eventTitle)}&st=${formatDate(utcStartDate)}&et=${formatDate(utcEndDate)}&desc=${encodeURIComponent(eventDetails)}&in_loc=${encodeURIComponent(location)}`
                };

                // Show calendar options modal or directly open Google Calendar
                showCalendarOptions(calendarOptions);

            } catch (error) {
                console.error('Error creating calendar event:', error);
                alert('Error creating calendar event. Please add the event manually.');
            }
        }

        function showCalendarOptions(calendarOptions) {
            // Create a simple modal for calendar selection
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.5);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 10000;
            `;

            const content = document.createElement('div');
            content.style.cssText = `
                background: white;
                padding: 30px;
                border-radius: 12px;
                max-width: 400px;
                width: 90%;
                text-align: center;
                box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            `;

            content.innerHTML = `
                <h3 style="margin: 0 0 20px 0; color: #333;">Add to Calendar</h3>
                <p style="margin: 0 0 25px 0; color: #666;">Choose your preferred calendar:</p>
                <div style="display: flex; flex-direction: column; gap: 12px;">
                    <button onclick="window.open('${calendarOptions.google}', '_blank'); document.body.removeChild(this.closest('.calendar-modal'))"
                            style="padding: 12px 20px; background: #4285f4; color: white; border: none; border-radius: 6px; cursor: pointer; font-size: 14px;">
                        📅 Google Calendar
                    </button>
                    <button onclick="window.open('${calendarOptions.outlook}', '_blank'); document.body.removeChild(this.closest('.calendar-modal'))"
                            style="padding: 12px 20px; background: #0078d4; color: white; border: none; border-radius: 6px; cursor: pointer; font-size: 14px;">
                        📅 Outlook Calendar
                    </button>
                    <button onclick="window.open('${calendarOptions.yahoo}', '_blank'); document.body.removeChild(this.closest('.calendar-modal'))"
                            style="padding: 12px 20px; background: #7b1fa2; color: white; border: none; border-radius: 6px; cursor: pointer; font-size: 14px;">
                        📅 Yahoo Calendar
                    </button>
                    <button onclick="document.body.removeChild(this.closest('.calendar-modal'))"
                            style="padding: 12px 20px; background: #f5f5f5; color: #333; border: none; border-radius: 6px; cursor: pointer; font-size: 14px; margin-top: 10px;">
                        Cancel
                    </button>
                </div>
            `;

            modal.appendChild(content);
            modal.className = 'calendar-modal';
            document.body.appendChild(modal);

            // Close on background click
            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    document.body.removeChild(modal);
                }
            });
        }

        function joinWhatsAppGroup() {
            // Replace with actual WhatsApp group link
            const whatsappGroupUrl = 'https://chat.whatsapp.com/your-group-link';
            window.open(whatsappGroupUrl, '_blank');
        }

        function completeRegistration() {
            closeModal();
        }

        // Hamburger Menu Functionality
        document.addEventListener('DOMContentLoaded', function() {
            const hamburgerMenu = document.getElementById('hamburgerMenu');
            const mobileNavDropdown = document.getElementById('mobileNavDropdown');
            const mobileNavLinks = document.querySelectorAll('.mobile-nav-link');

            // Toggle mobile menu
            hamburgerMenu.addEventListener('click', function() {
                hamburgerMenu.classList.toggle('active');
                mobileNavDropdown.classList.toggle('active');
            });

            // Close mobile menu when clicking on a link
            mobileNavLinks.forEach(link => {
                link.addEventListener('click', function() {
                    hamburgerMenu.classList.remove('active');
                    mobileNavDropdown.classList.remove('active');
                });
            });

            // Close mobile menu when clicking outside
            document.addEventListener('click', function(event) {
                const isClickInsideNav = hamburgerMenu.contains(event.target) ||
                                       mobileNavDropdown.contains(event.target);

                if (!isClickInsideNav && mobileNavDropdown.classList.contains('active')) {
                    hamburgerMenu.classList.remove('active');
                    mobileNavDropdown.classList.remove('active');
                }
            });

            // Close mobile menu on window resize to desktop
            window.addEventListener('resize', function() {
                if (window.innerWidth > 768) {
                    hamburgerMenu.classList.remove('active');
                    mobileNavDropdown.classList.remove('active');
                }
            });
        });
    </script>

</body>

</html>